<%@ Page Title="" Language="C#" CodeBehind="ToPay.aspx.cs" Inherits="Account.Web.ToPay" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="zh-CN">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="Content-Language" content="zh-cn">
    <meta name="apple-mobile-web-app-capable" content="no" />
    <meta name="apple-touch-fullscreen" content="yes" />
    <meta name="format-detection" content="telephone=no,email=no" />
    <meta name="apple-mobile-web-app-status-bar-style" content="white">
    <meta name="renderer" content="webkit" />
    <meta name="force-rendering" content="webkit" />
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-control" content="no-cache">
    <meta http-equiv="Cache" content="no-cache">
    <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <title>扫码支付<%=PageTitleConst.Default_Ext %></title>
    <style>
        /* 基础样式重置和字体设置 */
        html{font-size:62.5%;font-family:'helvetica neue',tahoma,arial,'hiragino sans gb','microsoft yahei','Simsun',sans-serif}
        body,div,ul,li,h1,p,a,img,strong,span,dt,dd,dl{margin:0;padding:0}
        body{line-height:1.333;font-size:12px;background:#f7f7f7;height:100%;max-width:640px;min-width:300px;min-height:100%;margin:0 auto}

        /* 链接样式 */
        a{color:#328CE5;text-decoration:none}
        a:hover{color:#2b8ae8}
        a:focus{outline:none}

        /* 列表样式 */
        li{list-style:none}

        /* 标题样式 */
        h1{font-family:"微软雅黑";font-size:15px;margin:5px 0;padding-bottom:2px;letter-spacing:2px}

        /* 主要容器样式 */
        .body{background:#f7f7f7;height:100%;max-width:640px;min-width:300px;min-height:100%;margin:0 auto}
        .mod-title{height:60px;line-height:60px;text-align:center;border-bottom:1px solid #ddd;background:#fff}
        .mod-ct{min-width:300px;max-width:640px;margin:15px auto 0;background:#fff url("./static/image/pay/wave.png") top center repeat-x;text-align:center;color:#333;border:1px solid #e5e5e5;border-top:none}

        /* 订单内容样式 */
        .mod-ct .order{font-size:20px;padding-top:10px}
        .mod-ct .amount{font-size:48px;margin-top:10px;font-family:sans-serif}
        .mod-ct .qr-image{margin:10px 0!important}

        /* 详情展开样式 */
        .mod-ct .detail{margin-top:10px;padding:0 0 10px}
        .mod-ct .detail .detail-ct{display:none;font-size:12px;text-align:right;line-height:28px}
        .mod-ct .detail .detail-ct dt{float:left}
        .mod-ct .detail-open{border-top:1px solid #e5e5e5}
        .mod-ct .detail .arrow{padding:6px 34px;border:1px solid #e5e5e5}
        .mod-ct .detail .arrow .ico-arrow{display:inline-block;width:20px;height:11px;background:url("./static/image/pay/wechat-pay.png") -25px -100px no-repeat}
        .mod-ct .detail-open .arrow .ico-arrow{background-position:0 -100px}
        .mod-ct .detail-open .detail-ct{display:block}

        /* 提示区域样式 */
        .mod-ct .tip{margin-top:20px;border-top:1px dashed #e5e5e5;padding:10px 0;position:relative}
        .mod-ct .tip .ico-scan{display:inline-block;width:56px;height:55px;background:url("./static/image/pay/wechat-pay.png") 0 0 no-repeat;vertical-align:middle}
        .mod-ct .tip .tip-text{display:inline-block;vertical-align:middle;text-align:left;margin-left:23px;font-size:16px;line-height:28px}

        /* 时间显示样式 */
        .time-item strong{background:#3ec742;color:#fff;line-height:25px;font-size:15px;font-family:Arial;padding:0 10px;margin-right:10px;border-radius:5px;box-shadow:1px 1px 3px rgba(0,0,0,0.2)}

        /* 支付方式选择样式 */
        .pay_clickli{border:2px solid #049ff1!important}
        .pay_down_ico{display:none;width:25px;height:25px;position:absolute;bottom:0;right:0;padding:0;background:url(./static/image/pay/pay_down_ico.png) no-repeat}
        .pay_clickli .pay_down_ico{display:block}
        .pay_ul2{float:left;margin-top:10px}
        .pay_ul2 li{border-radius:3px;width:140px;height:40px;float:left;margin-right:20px;border:1px solid #dbdbdb;position:relative;text-align:center;cursor:pointer}
        .pay_ul2 li div{text-align:center;line-height:40px;font-weight:normal}
    </style>
</head>
<body>
    <div class="body" id="body">
        <h1 class="mod-title" style="display:none;flex-wrap:nowrap">
            <ul class="pay_ul2" style="margin:8px auto 0">
                <li id="alipayLi" class="pay_clickli"><img class="pay_fs" src="./static/image/pay/alipay.jpg" title="支付宝" onclick="changePay(0)"><div class="pay_down_ico"></div></li>
                <li id="wxpayLi"><img class="pay_fs" src="./static/image/pay/weixin.jpg" title="微信" onclick="changePay(1)"><div class="pay_down_ico"></div></li>
            </ul>
        </h1>
        <div class="mod-ct" id="loadingDiv"><img id="loading" src="./static/image/pay/loading.gif" style="width:200px;height:200px"></div>
        <div class="mod-ct" id="orderDiv" style="display:none">
            <div class="order"></div>
            <div class="amount" id="timeOut" style="font-size:25px">
                <p style="display:flex;align-items:center;justify-content:center">
                    <img id="qqHead" style="width:40px;height:40px;background-size:100% 100%;margin:5px;border-radius:30px;display:none">
                    <span id="strRemark"></span>
                </p>
                <h1 id="timeOutTip" style="font-size:22px;display:none">
                    <span style="color:red">支付超时,订单已关闭！<br>请重新下单或【<a id="keFuQQ1" href="javascript:void(0)" target="_blank">联系客服</a>】</span>
                </h1>
            </div>
            <div id="orderbody">
                <div class="amount" id="money"></div>
                <div class="qrcode-img-wrapper"><div class="qrcode-img-area"><div class="qr-image" id="qrcode"></div></div></div>
                <div class="time-item">
                    <strong id="hour_show">0时</strong><strong id="minute_show">0分</strong><strong id="second_show">0秒</strong>
                    <div class="time-item" id="msg">
                        <h1><span style="color:blue;display:none" id="lblYouHui">请务必与订单金额一致，以免订单失败！<br></span>
                        <h1><span style="color:red">支付完成后，请耐心等待页面跳转<br>【有问题点此<a id="keFuQQ" href="javascript:void(0)" target="_blank">联系客服</a>】</span></h1></h1>
                    </div>
                </div>
                <div class="tip">
                    <div class="ico-scan"></div>
                    <div class="tip-text"><p>使用 <b id="payTypeMsg">支付宝</b> 扫一扫</p><p>扫描二维码完成支付</p></div>
                    <div class="detail" id="orderDetail">
                        <dl class="detail-ct" id="desc" style="display:none">
                            <dt>金额</dt><dd>￥<b id="strPrice"></b> 元</dd>
                            <dt>订单单号：</dt><dd><b id="strOrderId"></b></dd>
                            <dt>商户单号：</dt><dd><b id="strPayId"></b></dd>
                            <dt>创建时间：</dt><dd><b id="strDate"></b></dd>
                            <dt>状态</dt><dd>等待支付</dd>
                        </dl><br>
                        <a href="javascript:void(0)" class="arrow" onclick="showDetail()"><i class="ico-arrow"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        var qrcode=function(){var t=function(t,r){var e=t,n=g[r],o=null,i=0,a=null,u=[],f={},c=function(t,r){o=function(t){for(var r=new Array(t),e=0;e<t;e+=1){r[e]=new Array(t);for(var n=0;n<t;n+=1)r[e][n]=null}return r}(i=4*e+17),l(0,0),l(i-7,0),l(0,i-7),s(),h(),d(t,r),e>=7&&v(t),null==a&&(a=p(e,n,u)),w(a,r)},l=function(t,r){for(var e=-1;e<=7;e+=1)if(!(t+e<=-1||i<=t+e))for(var n=-1;n<=7;n+=1)r+n<=-1||i<=r+n||(o[t+e][r+n]=0<=e&&e<=6&&(0==n||6==n)||0<=n&&n<=6&&(0==e||6==e)||2<=e&&e<=4&&2<=n&&n<=4)},h=function(){for(var t=8;t<i-8;t+=1)null==o[t][6]&&(o[t][6]=t%2==0);for(var r=8;r<i-8;r+=1)null==o[6][r]&&(o[6][r]=r%2==0)},s=function(){for(var t=B.getPatternPosition(e),r=0;r<t.length;r+=1)for(var n=0;n<t.length;n+=1){var i=t[r],a=t[n];if(null==o[i][a])for(var u=-2;u<=2;u+=1)for(var f=-2;f<=2;f+=1)o[i+u][a+f]=-2==u||2==u||-2==f||2==f||0==u&&0==f}},v=function(t){for(var r=B.getBCHTypeNumber(e),n=0;n<18;n+=1){var a=!t&&1==(r>>n&1);o[Math.floor(n/3)][n%3+i-8-3]=a}for(n=0;n<18;n+=1){a=!t&&1==(r>>n&1);o[n%3+i-8-3][Math.floor(n/3)]=a}},d=function(t,r){for(var e=n<<3|r,a=B.getBCHTypeInfo(e),u=0;u<15;u+=1){var f=!t&&1==(a>>u&1);u<6?o[u][8]=f:u<8?o[u+1][8]=f:o[i-15+u][8]=f}for(u=0;u<15;u+=1){f=!t&&1==(a>>u&1);u<8?o[8][i-u-1]=f:u<9?o[8][15-u-1+1]=f:o[8][15-u-1]=f}o[i-8][8]=!t},w=function(t,r){for(var e=-1,n=i-1,a=7,u=0,f=B.getMaskFunction(r),c=i-1;c>0;c-=2)for(6==c&&(c-=1);;){for(var g=0;g<2;g+=1)if(null==o[n][c-g]){var l=!1;u<t.length&&(l=1==(t[u]>>>a&1)),f(n,c-g)&&(l=!l),o[n][c-g]=l,-1==(a-=1)&&(u+=1,a=7)}if((n+=e)<0||i<=n){n-=e,e=-e;break}}},p=function(t,r,e){for(var n=A.getRSBlocks(t,r),o=b(),i=0;i<e.length;i+=1){var a=e[i];o.put(a.getMode(),4),o.put(a.getLength(),B.getLengthInBits(a.getMode(),t)),a.write(o)}var u=0;for(i=0;i<n.length;i+=1)u+=n[i].dataCount;if(o.getLengthInBits()>8*u)throw"code length overflow. ("+o.getLengthInBits()+">"+8*u+")";for(o.getLengthInBits()+4<=8*u&&o.put(0,4);o.getLengthInBits()%8!=0;)o.putBit(!1);for(;!(o.getLengthInBits()>=8*u||(o.put(236,8),o.getLengthInBits()>=8*u));)o.put(17,8);return function(t,r){for(var e=0,n=0,o=0,i=new Array(r.length),a=new Array(r.length),u=0;u<r.length;u+=1){var f=r[u].dataCount,c=r[u].totalCount-f;n=Math.max(n,f),o=Math.max(o,c),i[u]=new Array(f);for(var g=0;g<i[u].length;g+=1)i[u][g]=255&t.getBuffer()[g+e];e+=f;var l=B.getErrorCorrectPolynomial(c),h=k(i[u],l.getLength()-1).mod(l);for(a[u]=new Array(l.getLength()-1),g=0;g<a[u].length;g+=1){var s=g+h.getLength()-a[u].length;a[u][g]=s>=0?h.getAt(s):0}}var v=0;for(g=0;g<r.length;g+=1)v+=r[g].totalCount;var d=new Array(v),w=0;for(g=0;g<n;g+=1)for(u=0;u<r.length;u+=1)g<i[u].length&&(d[w]=i[u][g],w+=1);for(g=0;g<o;g+=1)for(u=0;u<r.length;u+=1)g<a[u].length&&(d[w]=a[u][g],w+=1);return d}(o,n)};f.addData=function(t,r){var e=null;switch(r=r||"Byte"){case"Numeric":e=M(t);break;case"Alphanumeric":e=x(t);break;case"Byte":e=m(t);break;case"Kanji":e=L(t);break;default:throw"mode:"+r}u.push(e),a=null},f.isDark=function(t,r){if(t<0||i<=t||r<0||i<=r)throw t+","+r;return o[t][r]},f.getModuleCount=function(){return i},f.make=function(){if(e<1){for(var t=1;t<40;t++){for(var r=A.getRSBlocks(t,n),o=b(),i=0;i<u.length;i++){var a=u[i];o.put(a.getMode(),4),o.put(a.getLength(),B.getLengthInBits(a.getMode(),t)),a.write(o)}var g=0;for(i=0;i<r.length;i++)g+=r[i].dataCount;if(o.getLengthInBits()<=8*g)break}e=t}c(!1,function(){for(var t=0,r=0,e=0;e<8;e+=1){c(!0,e);var n=B.getLostPoint(f);(0==e||t>n)&&(t=n,r=e)}return r}())},f.createTableTag=function(t,r){t=t||2;var e="";e+='<table style="',e+=" border-width: 0px; border-style: none;",e+=" border-collapse: collapse;",e+=" padding: 0px; margin: "+(r=void 0===r?4*t:r)+"px;",e+='">',e+="<tbody>";for(var n=0;n<f.getModuleCount();n+=1){e+="<tr>";for(var o=0;o<f.getModuleCount();o+=1)e+='<td style="',e+=" border-width: 0px; border-style: none;",e+=" border-collapse: collapse;",e+=" padding: 0px; margin: 0px;",e+=" width: "+t+"px;",e+=" height: "+t+"px;",e+=" background-color: ",e+=f.isDark(n,o)?"#000000":"#ffffff",e+=";",e+='"/>';e+="</tr>"}return e+="</tbody>",e+="</table>"},f.createSvgTag=function(t,r,e,n){var o={};"object"==typeof arguments[0]&&(t=(o=arguments[0]).cellSize,r=o.margin,e=o.alt,n=o.title),t=t||2,r=void 0===r?4*t:r,(e="string"==typeof e?{text:e}:e||{}).text=e.text||null,e.id=e.text?e.id||"qrcode-description":null,(n="string"==typeof n?{text:n}:n||{}).text=n.text||null,n.id=n.text?n.id||"qrcode-title":null;var i,a,u,c,g=f.getModuleCount()*t+2*r,l="";for(c="l"+t+",0 0,"+t+" -"+t+",0 0,-"+t+"z ",l+='<svg version="1.1" xmlns="http://www.w3.org/2000/svg"',l+=o.scalable?"":' width="'+g+'px" height="'+g+'px"',l+=' viewBox="0 0 '+g+" "+g+'" ',l+=' preserveAspectRatio="xMinYMin meet"',l+=n.text||e.text?' role="img" aria-labelledby="'+y([n.id,e.id].join(" ").trim())+'"':"",l+=">",l+=n.text?'<title id="'+y(n.id)+'">'+y(n.text)+"</title>":"",l+=e.text?'<description id="'+y(e.id)+'">'+y(e.text)+"</description>":"",l+='<rect width="100%" height="100%" fill="white" cx="0" cy="0"/>',l+='<path d="',a=0;a<f.getModuleCount();a+=1)for(u=a*t+r,i=0;i<f.getModuleCount();i+=1)f.isDark(a,i)&&(l+="M"+(i*t+r)+","+u+c);return l+='" stroke="transparent" fill="black"/>',l+="</svg>"},f.createDataURL=function(t,r){t=t||2,r=void 0===r?4*t:r;var e=f.getModuleCount()*t+2*r,n=r,o=e-r;return I(e,e,(function(r,e){if(n<=r&&r<o&&n<=e&&e<o){var i=Math.floor((r-n)/t),a=Math.floor((e-n)/t);return f.isDark(a,i)?0:1}return 1}))},f.createImgTag=function(t,r,e){t=t||2,r=void 0===r?4*t:r;var n=f.getModuleCount()*t+2*r,o="";return o+="<img",o+=' src="',o+=f.createDataURL(t,r),o+='"',o+=' width="',o+=n,o+='"',o+=' height="',o+=n,o+='"',e&&(o+=' alt="',o+=y(e),o+='"'),o+="/>"};var y=function(t){for(var r="",e=0;e<t.length;e+=1){var n=t.charAt(e);switch(n){case"<":r+="&lt;";break;case">":r+="&gt;";break;case"&":r+="&amp;";break;case'"':r+="&quot;";break;default:r+=n}}return r};return f.createASCII=function(t,r){if((t=t||1)<2)return function(t){t=void 0===t?2:t;var r,e,n,o,i,a=1*f.getModuleCount()+2*t,u=t,c=a-t,g={"██":"█","█ ":"▀"," █":"▄","  ":" "},l={"██":"▀","█ ":"▀"," █":" ","  ":" "},h="";for(r=0;r<a;r+=2){for(n=Math.floor((r-u)/1),o=Math.floor((r+1-u)/1),e=0;e<a;e+=1)i="█",u<=e&&e<c&&u<=r&&r<c&&f.isDark(n,Math.floor((e-u)/1))&&(i=" "),u<=e&&e<c&&u<=r+1&&r+1<c&&f.isDark(o,Math.floor((e-u)/1))?i+=" ":i+="█",h+=t<1&&r+1>=c?l[i]:g[i];h+="\n"}return a%2&&t>0?h.substring(0,h.length-a-1)+Array(a+1).join("▀"):h.substring(0,h.length-1)}(r);t-=1,r=void 0===r?2*t:r;var e,n,o,i,a=f.getModuleCount()*t+2*r,u=r,c=a-r,g=Array(t+1).join("██"),l=Array(t+1).join("  "),h="",s="";for(e=0;e<a;e+=1){for(o=Math.floor((e-u)/t),s="",n=0;n<a;n+=1)i=1,u<=n&&n<c&&u<=e&&e<c&&f.isDark(o,Math.floor((n-u)/t))&&(i=0),s+=i?g:l;for(o=0;o<t;o+=1)h+=s+"\n"}return h.substring(0,h.length-1)},f.renderTo2dContext=function(t,r){r=r||2;for(var e=f.getModuleCount(),n=0;n<e;n++)for(var o=0;o<e;o++)t.fillStyle=f.isDark(n,o)?"black":"white",t.fillRect(n*r,o*r,r,r)},f};t.stringToBytes=(t.stringToBytesFuncs={default:function(t){for(var r=[],e=0;e<t.length;e+=1){var n=t.charCodeAt(e);r.push(255&n)}return r}}).default,t.createStringToBytes=function(t,r){var e=function(){for(var e=S(t),n=function(){var t=e.read();if(-1==t)throw"eof";return t},o=0,i={};;){var a=e.read();if(-1==a)break;var u=n(),f=n()<<8|n();i[String.fromCharCode(a<<8|u)]=f,o+=1}if(o!=r)throw o+" != "+r;return i}(),n="?".charCodeAt(0);return function(t){for(var r=[],o=0;o<t.length;o+=1){var i=t.charCodeAt(o);if(i<128)r.push(i);else{var a=e[t.charAt(o)];"number"==typeof a?(255&a)==a?r.push(a):(r.push(a>>>8),r.push(255&a)):r.push(n)}}return r}};var r,e,n,o,i,a=1,u=2,f=4,c=8,g={L:1,M:0,Q:3,H:2},l=0,h=1,s=2,v=3,d=4,w=5,p=6,y=7,B=(r=[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],e=1335,n=7973,i=function(t){for(var r=0;0!=t;)r+=1,t>>>=1;return r},(o={}).getBCHTypeInfo=function(t){for(var r=t<<10;i(r)-i(e)>=0;)r^=e<<i(r)-i(e);return 21522^(t<<10|r)},o.getBCHTypeNumber=function(t){for(var r=t<<12;i(r)-i(n)>=0;)r^=n<<i(r)-i(n);return t<<12|r},o.getPatternPosition=function(t){return r[t-1]},o.getMaskFunction=function(t){switch(t){case l:return function(t,r){return(t+r)%2==0};case h:return function(t,r){return t%2==0};case s:return function(t,r){return r%3==0};case v:return function(t,r){return(t+r)%3==0};case d:return function(t,r){return(Math.floor(t/2)+Math.floor(r/3))%2==0};case w:return function(t,r){return t*r%2+t*r%3==0};case p:return function(t,r){return(t*r%2+t*r%3)%2==0};case y:return function(t,r){return(t*r%3+(t+r)%2)%2==0};default:throw"bad maskPattern:"+t}},o.getErrorCorrectPolynomial=function(t){for(var r=k([1],0),e=0;e<t;e+=1)r=r.multiply(k([1,C.gexp(e)],0));return r},o.getLengthInBits=function(t,r){if(1<=r&&r<10)switch(t){case a:return 10;case u:return 9;case f:case c:return 8;default:throw"mode:"+t}else if(r<27)switch(t){case a:return 12;case u:return 11;case f:return 16;case c:return 10;default:throw"mode:"+t}else{if(!(r<41))throw"type:"+r;switch(t){case a:return 14;case u:return 13;case f:return 16;case c:return 12;default:throw"mode:"+t}}},o.getLostPoint=function(t){for(var r=t.getModuleCount(),e=0,n=0;n<r;n+=1)for(var o=0;o<r;o+=1){for(var i=0,a=t.isDark(n,o),u=-1;u<=1;u+=1)if(!(n+u<0||r<=n+u))for(var f=-1;f<=1;f+=1)o+f<0||r<=o+f||0==u&&0==f||a==t.isDark(n+u,o+f)&&(i+=1);i>5&&(e+=3+i-5)}for(n=0;n<r-1;n+=1)for(o=0;o<r-1;o+=1){var c=0;t.isDark(n,o)&&(c+=1),t.isDark(n+1,o)&&(c+=1),t.isDark(n,o+1)&&(c+=1),t.isDark(n+1,o+1)&&(c+=1),0!=c&&4!=c||(e+=3)}for(n=0;n<r;n+=1)for(o=0;o<r-6;o+=1)t.isDark(n,o)&&!t.isDark(n,o+1)&&t.isDark(n,o+2)&&t.isDark(n,o+3)&&t.isDark(n,o+4)&&!t.isDark(n,o+5)&&t.isDark(n,o+6)&&(e+=40);for(o=0;o<r;o+=1)for(n=0;n<r-6;n+=1)t.isDark(n,o)&&!t.isDark(n+1,o)&&t.isDark(n+2,o)&&t.isDark(n+3,o)&&t.isDark(n+4,o)&&!t.isDark(n+5,o)&&t.isDark(n+6,o)&&(e+=40);var g=0;for(o=0;o<r;o+=1)for(n=0;n<r;n+=1)t.isDark(n,o)&&(g+=1);return e+=Math.abs(100*g/r/r-50)/5*10},o),C=function(){for(var t=new Array(256),r=new Array(256),e=0;e<8;e+=1)t[e]=1<<e;for(e=8;e<256;e+=1)t[e]=t[e-4]^t[e-5]^t[e-6]^t[e-8];for(e=0;e<255;e+=1)r[t[e]]=e;var n={glog:function(t){if(t<1)throw"glog("+t+")";return r[t]},gexp:function(r){for(;r<0;)r+=255;for(;r>=256;)r-=255;return t[r]}};return n}();function k(t,r){if(void 0===t.length)throw t.length+"/"+r;var e=function(){for(var e=0;e<t.length&&0==t[e];)e+=1;for(var n=new Array(t.length-e+r),o=0;o<t.length-e;o+=1)n[o]=t[o+e];return n}(),n={getAt:function(t){return e[t]},getLength:function(){return e.length},multiply:function(t){for(var r=new Array(n.getLength()+t.getLength()-1),e=0;e<n.getLength();e+=1)for(var o=0;o<t.getLength();o+=1)r[e+o]^=C.gexp(C.glog(n.getAt(e))+C.glog(t.getAt(o)));return k(r,0)},mod:function(t){if(n.getLength()-t.getLength()<0)return n;for(var r=C.glog(n.getAt(0))-C.glog(t.getAt(0)),e=new Array(n.getLength()),o=0;o<n.getLength();o+=1)e[o]=n.getAt(o);for(o=0;o<t.getLength();o+=1)e[o]^=C.gexp(C.glog(t.getAt(o))+r);return k(e,0).mod(t)}};return n}var A=function(){var t=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12,7,37,13],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],r=function(t,r){var e={};return e.totalCount=t,e.dataCount=r,e},e={};return e.getRSBlocks=function(e,n){var o=function(r,e){switch(e){case g.L:return t[4*(r-1)+0];case g.M:return t[4*(r-1)+1];case g.Q:return t[4*(r-1)+2];case g.H:return t[4*(r-1)+3];default:return}}(e,n);if(void 0===o)throw"bad rs block @ typeNumber:"+e+"/errorCorrectionLevel:"+n;for(var i=o.length/3,a=[],u=0;u<i;u+=1)for(var f=o[3*u+0],c=o[3*u+1],l=o[3*u+2],h=0;h<f;h+=1)a.push(r(c,l));return a},e}(),b=function(){var t=[],r=0,e={getBuffer:function(){return t},getAt:function(r){var e=Math.floor(r/8);return 1==(t[e]>>>7-r%8&1)},put:function(t,r){for(var n=0;n<r;n+=1)e.putBit(1==(t>>>r-n-1&1))},getLengthInBits:function(){return r},putBit:function(e){var n=Math.floor(r/8);t.length<=n&&t.push(0),e&&(t[n]|=128>>>r%8),r+=1}};return e},M=function(t){var r=a,e=t,n={getMode:function(){return r},getLength:function(t){return e.length},write:function(t){for(var r=e,n=0;n+2<r.length;)t.put(o(r.substring(n,n+3)),10),n+=3;n<r.length&&(r.length-n==1?t.put(o(r.substring(n,n+1)),4):r.length-n==2&&t.put(o(r.substring(n,n+2)),7))}},o=function(t){for(var r=0,e=0;e<t.length;e+=1)r=10*r+i(t.charAt(e));return r},i=function(t){if("0"<=t&&t<="9")return t.charCodeAt(0)-"0".charCodeAt(0);throw"illegal char :"+t};return n},x=function(t){var r=u,e=t,n={getMode:function(){return r},getLength:function(t){return e.length},write:function(t){for(var r=e,n=0;n+1<r.length;)t.put(45*o(r.charAt(n))+o(r.charAt(n+1)),11),n+=2;n<r.length&&t.put(o(r.charAt(n)),6)}},o=function(t){if("0"<=t&&t<="9")return t.charCodeAt(0)-"0".charCodeAt(0);if("A"<=t&&t<="Z")return t.charCodeAt(0)-"A".charCodeAt(0)+10;switch(t){case" ":return 36;case"$":return 37;case"%":return 38;case"*":return 39;case"+":return 40;case"-":return 41;case".":return 42;case"/":return 43;case":":return 44;default:throw"illegal char :"+t}};return n},m=function(r){var e=f,n=t.stringToBytes(r),o={getMode:function(){return e},getLength:function(t){return n.length},write:function(t){for(var r=0;r<n.length;r+=1)t.put(n[r],8)}};return o},L=function(r){var e=c,n=t.stringToBytesFuncs.SJIS;if(!n)throw"sjis not supported.";!function(t,r){var e=n("友");if(2!=e.length||38726!=(e[0]<<8|e[1]))throw"sjis not supported."}();var o=n(r),i={getMode:function(){return e},getLength:function(t){return~~(o.length/2)},write:function(t){for(var r=o,e=0;e+1<r.length;){var n=(255&r[e])<<8|255&r[e+1];if(33088<=n&&n<=40956)n-=33088;else{if(!(57408<=n&&n<=60351))throw"illegal char at "+(e+1)+"/"+n;n-=49472}n=192*(n>>>8&255)+(255&n),t.put(n,13),e+=2}if(e<r.length)throw"illegal char at "+(e+1)}};return i},D=function(){var t=[],r={writeByte:function(r){t.push(255&r)},writeShort:function(t){r.writeByte(t),r.writeByte(t>>>8)},writeBytes:function(t,e,n){e=e||0,n=n||t.length;for(var o=0;o<n;o+=1)r.writeByte(t[o+e])},writeString:function(t){for(var e=0;e<t.length;e+=1)r.writeByte(t.charCodeAt(e))},toByteArray:function(){return t},toString:function(){var r="";r+="[";for(var e=0;e<t.length;e+=1)e>0&&(r+=","),r+=t[e];return r+="]"}};return r},S=function(t){var r=t,e=0,n=0,o=0,i={read:function(){for(;o<8;){if(e>=r.length){if(0==o)return-1;throw"unexpected end of file./"+o}var t=r.charAt(e);if(e+=1,"="==t)return o=0,-1;t.match(/^\s$/)||(n=n<<6|a(t.charCodeAt(0)),o+=6)}var i=n>>>o-8&255;return o-=8,i}},a=function(t){if(65<=t&&t<=90)return t-65;if(97<=t&&t<=122)return t-97+26;if(48<=t&&t<=57)return t-48+52;if(43==t)return 62;if(47==t)return 63;throw"c:"+t};return i},I=function(t,r,e){for(var n=function(t,r){var e=t,n=r,o=new Array(t*r),i={setPixel:function(t,r,n){o[r*e+t]=n},write:function(t){t.writeString("GIF87a"),t.writeShort(e),t.writeShort(n),t.writeByte(128),t.writeByte(0),t.writeByte(0),t.writeByte(0),t.writeByte(0),t.writeByte(0),t.writeByte(255),t.writeByte(255),t.writeByte(255),t.writeString(","),t.writeShort(0),t.writeShort(0),t.writeShort(e),t.writeShort(n),t.writeByte(0);var r=a(2);t.writeByte(2);for(var o=0;r.length-o>255;)t.writeByte(255),t.writeBytes(r,o,255),o+=255;t.writeByte(r.length-o),t.writeBytes(r,o,r.length-o),t.writeByte(0),t.writeString(";")}},a=function(t){for(var r=1<<t,e=1+(1<<t),n=t+1,i=u(),a=0;a<r;a+=1)i.add(String.fromCharCode(a));i.add(String.fromCharCode(r)),i.add(String.fromCharCode(e));var f,c,g,l=D(),h=(f=l,c=0,g=0,{write:function(t,r){if(t>>>r!=0)throw"length over";for(;c+r>=8;)f.writeByte(255&(t<<c|g)),r-=8-c,t>>>=8-c,g=0,c=0;g|=t<<c,c+=r},flush:function(){c>0&&f.writeByte(g)}});h.write(r,n);var s=0,v=String.fromCharCode(o[s]);for(s+=1;s<o.length;){var d=String.fromCharCode(o[s]);s+=1,i.contains(v+d)?v+=d:(h.write(i.indexOf(v),n),i.size()<4095&&(i.size()==1<<n&&(n+=1),i.add(v+d)),v=d)}return h.write(i.indexOf(v),n),h.write(e,n),h.flush(),l.toByteArray()},u=function(){var t={},r=0,e={add:function(n){if(e.contains(n))throw"dup key:"+n;t[n]=r,r+=1},size:function(){return r},indexOf:function(r){return t[r]},contains:function(r){return void 0!==t[r]}};return e};return i}(t,r),o=0;o<r;o+=1)for(var i=0;i<t;i+=1)n.setPixel(i,o,e(i,o));var a=D();n.write(a);for(var u=function(){var t=0,r=0,e=0,n="",o={},i=function(t){n+=String.fromCharCode(a(63&t))},a=function(t){if(t<0);else{if(t<26)return 65+t;if(t<52)return t-26+97;if(t<62)return t-52+48;if(62==t)return 43;if(63==t)return 47}throw"n:"+t};return o.writeByte=function(n){for(t=t<<8|255&n,r+=8,e+=1;r>=6;)i(t>>>r-6),r-=6},o.flush=function(){if(r>0&&(i(t<<6-r),t=0,r=0),e%3!=0)for(var o=3-e%3,a=0;a<o;a+=1)n+="="},o.toString=function(){return n},o}(),f=a.toByteArray(),c=0;c<f.length;c+=1)u.writeByte(f[c]);return u.flush(),"data:image/gif;base64,"+u};return t}();qrcode.stringToBytesFuncs["UTF-8"]=function(t){return function(t){for(var r=[],e=0;e<t.length;e++){var n=t.charCodeAt(e);n<128?r.push(n):n<2048?r.push(192|n>>6,128|63&n):n<55296||n>=57344?r.push(224|n>>12,128|n>>6&63,128|63&n):(e++,n=65536+((1023&n)<<10|1023&t.charCodeAt(e)),r.push(240|n>>18,128|n>>12&63,128|n>>6&63,128|63&n))}return r}(t)};
    </script>
    <script>
        // 配置常量
        var CONFIG = {
            QRCODE_SIZE: 230,
            CHECK_INTERVAL: 1800,
            TICK_INTERVAL: 1000,
            MSG_TIMEOUT: 3000,
            COLORS: {
                SUCCESS: '#52c41a',
                ERROR: 'red',
                PRIMARY: '#049ff1'
            }
        };

        // 元素缓存
        var Elements = {
            cache: {},
            get: function(selector) {
                if (!this.cache[selector]) {
                    this.cache[selector] = typeof selector === 'string' ? document.querySelector(selector) : selector;
                }
                return this.cache[selector];
            },
            getById: function(id) {
                if (!this.cache['#' + id]) {
                    this.cache['#' + id] = document.getElementById(id);
                }
                return this.cache['#' + id];
            }
        };

        // 简化的选择器函数
        var $ = function(s) { return Elements.get(s); };

        // DOM工具类
        var DOMUtils = {
            addClass: function(e, c) { e && e.classList && e.classList.add(c); },
            removeClass: function(e, c) { e && e.classList && e.classList.remove(c); },
            hasClass: function(e, c) { return e && e.classList && e.classList.contains(c); },
            show: function(e) { if (e) e.style.display = ''; },
            hide: function(e) { if (e) e.style.display = 'none'; },
            html: function(e, c) { if (e) return c !== undefined ? e.innerHTML = c : e.innerHTML; },
            text: function(e, c) { if (e) return c !== undefined ? e.textContent = c : e.textContent; },
            css: function(e, p, v) {
                if (e && e.style) {
                    if (typeof p === 'object') {
                        for (var k in p) e.style[k] = p[k];
                    } else return v !== undefined ? e.style[p] = v : getComputedStyle(e)[p];
                }
            },
            attr: function(e, n, v) { if (e) return v !== undefined ? e.setAttribute(n, v) : e.getAttribute(n); }
        };

        // AJAX工具类
        var AjaxUtils = {
            post: function(u, d, c) {
                var x = new XMLHttpRequest();
                x.open('POST', u, true);
                x.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                x.onreadystatechange = function() {
                    if (x.readyState === 4 && x.status === 200) {
                        try {
                            var r = JSON.parse(x.responseText);
                            if (c) c(r);
                        } catch (e) {
                            console.error('JSON parse error:', e);
                        }
                    }
                };
                x.send(d);
            }
        };

        // 二维码生成工具
        var QRCode = {
            generate: function(t, o) {
                o = o || {};
                var s = o.width || o.height || CONFIG.QRCODE_SIZE,
                    f = o.foreground || '#000000',
                    b = o.background || '#ffffff',
                    c = document.createElement('canvas');
                c.width = s;
                c.height = s;
                var x = c.getContext('2d');
                try {
                    var q = qrcode(0, 'M');
                    q.addData(t);
                    q.make();
                    var m = q.getModuleCount(), z = s / m;
                    x.fillStyle = b;
                    x.fillRect(0, 0, s, s);
                    x.fillStyle = f;
                    for (var r = 0; r < m; r++)
                        for (var l = 0; l < m; l++)
                            q.isDark(r, l) && x.fillRect(l * z, r * z, z, z);
                } catch (e) {
                    console.error('QR Code generation error:', e);
                }
                return c;
            }
        };

        // 全局变量
        var customMsgTimer = null;
        var intDiff = 0; // 剩余时间（秒）
        // 消息提示模块
        var MessageModule = {
            show: function(content, timeout) {
                content = content.replace(/\n/g, '<br>');
                var msgLayer = Elements.getById("customMsgLayer");

                if (!msgLayer) {
                    msgLayer = document.createElement('div');
                    msgLayer.id = 'customMsgLayer';
                    document.body.appendChild(msgLayer);
                    Elements.cache['#customMsgLayer'] = msgLayer;
                }

                var icon = '<span class="custom-msg-icon" style="display:inline-block;margin-right:10px;vertical-align:middle">' +
                          '<svg viewBox="0 0 1024 1024" width="30" height="30">' +
                          '<circle cx="512" cy="512" r="512" fill="' + CONFIG.COLORS.SUCCESS + '"/>' +
                          '<path d="M768 320L448 640l-192-192" stroke="#fff" stroke-width="80" stroke-linecap="round" stroke-linejoin="round" fill="none"/>' +
                          '</svg></span>';

                msgLayer.innerHTML = '<div class="custom-msg-content">' + icon + '<span class="custom-msg-text">' + content + '</span></div>';

                var alipayLi = Elements.getById("alipayLi");
                var leftPos = alipayLi ? alipayLi.offsetLeft + 25 : 50;

                DOMUtils.css(msgLayer, {
                    position: 'fixed',
                    top: '75px',
                    left: leftPos + 'px',
                    background: '#fff',
                    color: '#333',
                    border: '1px solid #D3D4D3',
                    borderRadius: '2px',
                    padding: '20px',
                    zIndex: '19891015',
                    boxShadow: '1px 1px 50px rgba(0,0,0,.3)',
                    fontSize: '14px',
                    minWidth: '180px',
                    maxWidth: '300px',
                    textAlign: 'center',
                    opacity: '0',
                    transition: 'opacity 0.3s ease'
                });

                var contentEl = msgLayer.querySelector('.custom-msg-content');
                if (contentEl) {
                    DOMUtils.css(contentEl, {
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        lineHeight: '24px'
                    });
                }

                var textEl = msgLayer.querySelector('.custom-msg-text');
                if (textEl) {
                    DOMUtils.css(textEl, {
                        verticalAlign: 'middle',
                        wordBreak: 'break-all'
                    });
                }

                setTimeout(function() { msgLayer.style.opacity = '1'; }, 10);

                if (customMsgTimer) clearTimeout(customMsgTimer);
                customMsgTimer = setTimeout(function() {
                    msgLayer.style.opacity = '0';
                    setTimeout(function() { msgLayer.style.display = 'none'; }, 300);
                }, timeout || CONFIG.MSG_TIMEOUT);
            }
        };


        // 时间格式化工具
        function formatDate(timestamp) {
            var date = new Date(timestamp);
            var pad = function(num) { return num > 9 ? num : "0" + num; };

            return date.getFullYear() + "-" +
                   pad(date.getMonth() + 1) + "-" +
                   pad(date.getDate()) + " " +
                   pad(date.getHours()) + ":" +
                   pad(date.getMinutes()) + ":" +
                   pad(date.getSeconds());
        }

        // 定时器管理模块
        var TimerModule = {
            tickTimer: null,
            checkTimer: null,

            start: function() {
                if (intDiff > 0) {
                    this.clear();
                    this.tickTimer = setInterval(function() {
                        TimerModule.payTick();
                        intDiff--;
                    }, CONFIG.TICK_INTERVAL);
                    this.checkTimer = setInterval(function() {
                        PaymentModule.check();
                    }, CONFIG.CHECK_INTERVAL);
                } else {
                    PaymentModule.timeout('');
                }
            },

            clear: function() {
                if (this.tickTimer) {
                    clearInterval(this.tickTimer);
                    this.tickTimer = null;
                }
                if (this.checkTimer) {
                    clearInterval(this.checkTimer);
                    this.checkTimer = null;
                }
            },

            payTick: function() {
                var d = 0, h = 0, m = 0, s = 0;

                if (intDiff > 0) {
                    d = Math.floor(intDiff / (60 * 60 * 24));
                    h = Math.floor(intDiff / (60 * 60)) - (d * 24);
                    m = Math.floor(intDiff / 60) - (d * 24 * 60) - (h * 60);
                    s = Math.floor(intDiff) - (d * 24 * 60 * 60) - (h * 60 * 60) - (m * 60);
                }

                var pad = function(num) { return num <= 9 ? '0' + num : num; };
                m = pad(m);
                s = pad(s);

                var hourShow = $('#hour_show');
                var minuteShow = $('#minute_show');
                var secondShow = $('#second_show');

                if (h > 0) {
                    DOMUtils.html(hourShow, '<s></s>' + h + '时');
                    DOMUtils.show(hourShow);
                } else {
                    DOMUtils.hide(hourShow);
                }

                DOMUtils.html(minuteShow, '<s></s>' + m + '分');
                DOMUtils.html(secondShow, '<s></s>' + s + '秒');

                if (h <= 0 && m <= 0 && s <= 0) {
                    PaymentModule.timeout('');
                    this.clear();
                }
            }
        };

        // 支付超时处理
        function qrcode_timeout(msg) {
            if (msg == null || msg == '') {
                DOMUtils.show($('#timeOutTip'));
            }
            DOMUtils.hide($('.mod-title'));
            DOMUtils.hide($('#orderbody'));
            DOMUtils.html($('#timeOut p span'), msg);
            DOMUtils.css($('#timeOut'), 'color', CONFIG.COLORS.ERROR);
        }
        // 工具函数
        function getQueryString(name) {
            var regex = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            var result = regex.exec(window.location.search.substr(1));
            return result != null ? decodeURI(result[2]) : null;
        }

        // 支付模块
        var PaymentModule = {
            nowPayType: -1,
            wxUrl: "",
            zfbUrl: "",
            otherUrl: "",
            orderFrom: 0,

            // 切换支付方式UI
            switchPayType: function(payType) {
                var url = window.location.href;
                var isAlipay = payType == 0 || payType == 2;

                // 更新URL
                if (isAlipay) {
                    url = this.zfbUrl;
                } else {
                    url = this.wxUrl;
                }

                if (this.orderFrom != 0) url = this.otherUrl;
                if (url == null || url == "") url = window.location.href;

                // 更新UI状态
                this.updatePaymentUI(payType, isAlipay);

                // 更新二维码
                this.updateQRCode(url);

                // 发送支付类型变更请求
                this.changePayType(payType);
            },

            // 更新支付方式UI
            updatePaymentUI: function(payType, isAlipay) {
                var alipayLi = $('#alipayLi');
                var wxpayLi = $('#wxpayLi');
                var payTypeMsg = $('#payTypeMsg');

                // 清除所有选中状态
                DOMUtils.removeClass(alipayLi, 'pay_clickli');
                DOMUtils.removeClass(wxpayLi, 'pay_clickli');

                // 设置当前选中状态和文本
                if (isAlipay) {
                    DOMUtils.addClass(alipayLi, 'pay_clickli');
                    DOMUtils.text(payTypeMsg, '支付宝');
                } else {
                    DOMUtils.addClass(wxpayLi, 'pay_clickli');
                    DOMUtils.text(payTypeMsg, '微信');
                }
            },

            // 更新二维码
            updateQRCode: function(url) {
                var qrcodeEl = $('#qrcode');
                DOMUtils.html(qrcodeEl, "");

                if (url.indexOf("/static") == 0) {
                    DOMUtils.html(qrcodeEl, "<img src='" + url + "'>");
                } else {
                    var canvas = QRCode.generate(url, {
                        width: CONFIG.QRCODE_SIZE,
                        height: CONFIG.QRCODE_SIZE,
                        foreground: "#000000",
                        background: "#ffffff"
                    });
                    qrcodeEl.appendChild(canvas);
                }
            },

            // 发送支付类型变更请求
            changePayType: function(payType) {
                if (this.nowPayType == payType) {
                    var payTypeName = (payType == 0 ? "支付宝" : "微信");
                    MessageModule.show("请使用【" + payTypeName + "】扫码支付！<br>也可以在顶部切换其他付款方式！<br>", 2000);
                    return;
                }

                this.nowPayType = payType;
                var orderId = getQueryString("orderId");
                AjaxUtils.post("/code.aspx?op=changePayType", "orderId=" + orderId + "&payType=" + payType);

                var payTypeName = (payType == 0 ? "支付宝" : "微信");
                if (navigator.userAgent.match(/MicroMessenger\//i) && payType == 1) {
                    MessageModule.show("支付方式变更为【微信】，请重新扫码！<br>", 3000);
                } else {
                    MessageModule.show("请使用【" + payTypeName + "】扫码支付！<br>也可以在顶部切换其他付款方式！<br>", 2000);
                }
            },

            // 支付超时处理
            timeout: function(msg) {
                qrcode_timeout(msg);
            }
        };

        // 兼容旧函数名 - 供HTML onclick事件调用
        function changePay(payType) {
            PaymentModule.switchPayType(payType);
        }

        function showDetail() {
            var orderDetail = $('#orderDetail');
            var detailContent = $('#orderDetail .detail-ct');

            if (DOMUtils.hasClass(orderDetail, 'detail-open')) {
                DOMUtils.hide(detailContent);
                DOMUtils.removeClass(orderDetail, 'detail-open');
            } else {
                DOMUtils.show(detailContent);
                DOMUtils.addClass(orderDetail, 'detail-open');
            }
        }

        // 扩展支付模块 - 订单处理功能
        PaymentModule.initOrder = function() {
            var orderId = getQueryString("orderId");
            AjaxUtils.post("/code.aspx?op=getOrder", "orderId=" + orderId, function(data) {
                if (data.code == 1) {
                    PaymentModule.handleOrderData(data.data);
                } else {
                    PaymentModule.handleOrderError(data.msg);
                }
            });
        };

        PaymentModule.handleOrderData = function(orderData) {
            var timeLeft = 0;

            // 检查订单状态
            if (orderData.state >= 1) {
                PaymentModule.timeout("恭喜，订单支付成功!");
                if (orderData.returnUrl != null && orderData.returnUrl != '') {
                    window.location.href = orderData.returnUrl;
                    return;
                }
            } else {
                if (orderData.state != -1) {
                    timeLeft = new Date().getTime() - orderData.date;
                    timeLeft = orderData.timeOut * 60 - timeLeft / 1000;
                }

                // 设置支付URL
                PaymentModule.zfbUrl = orderData.zfbPayUrl;
                PaymentModule.wxUrl = orderData.wxPayUrl;
                PaymentModule.otherUrl = orderData.payUrl;
                PaymentModule.orderFrom = orderData.from;

                // 处理自动跳转
                if (navigator.userAgent.match(/Alipay/i) && PaymentModule.zfbUrl != null && PaymentModule.zfbUrl != '') {
                    window.location.href = PaymentModule.zfbUrl;
                } else if (navigator.userAgent.match(/MicroMessenger\//i) && PaymentModule.wxUrl != null && PaymentModule.wxUrl != '') {
                    if (orderData.payType != 1) {
                        orderData.payType = 1;
                        PaymentModule.changePayType(orderData.payType);
                        return;
                    }
                }
            }

            // 更新订单信息
            PaymentModule.updateOrderInfo(orderData);

            // 显示订单界面
            PaymentModule.showOrderInterface();

            // 启动定时器
            if (timeLeft > 0) {
                DOMUtils.css($('.mod-title'), 'display', 'flex');
                PaymentModule.switchPayType(orderData.payType);
            }

            window.intDiff = timeLeft;
            TimerModule.start();
            PaymentModule.check();

            // 设置客服信息
            if (orderData.qq != null) {
                PaymentModule.setupCustomerService(orderData.qq);
            }
        };

        PaymentModule.updateOrderInfo = function(orderData) {
            // 批量更新订单信息
            var updates = {
                'strRemark': orderData.remark,
                'money': "￥" + orderData.reallyPrice.toFixed(2),
                'strPrice': orderData.price,
                'strPayId': orderData.payId,
                'strOrderId': orderData.orderId,
                'strDate': formatDate(orderData.date)
            };

            Object.keys(updates).forEach(function(id) {
                var element = Elements.getById(id);
                if (element) {
                    if (id === 'strRemark' || id === 'money') {
                        element.innerText = updates[id];
                    } else {
                        DOMUtils.text(element, updates[id]);
                    }
                }
            });

            PaymentModule.nowPayType = orderData.payType;

            if (orderData.needUserPay) {
                DOMUtils.show($('#lblYouHui'));
            }
        };

        PaymentModule.showOrderInterface = function() {
            DOMUtils.show($('#orderDiv'));
            DOMUtils.hide($('#loadingDiv'));
        };

        PaymentModule.setupCustomerService = function(qq) {
            var qqHead = $('#qqHead');
            var keFuQQ = $('#keFuQQ');
            var keFuQQ1 = $('#keFuQQ1');

            DOMUtils.attr(qqHead, 'src', "https://q1.qlogo.cn/g?b=qq&nk=" + qq + "&s=100");
            DOMUtils.show(qqHead);

            var qqUrl = "http://wpa.qq.com/msgrd?v=3&uin=" + qq + "&site=qq&menu=yes";
            DOMUtils.attr(keFuQQ, 'href', qqUrl);
            DOMUtils.attr(keFuQQ1, 'href', qqUrl);
        };

        PaymentModule.handleOrderError = function(errorMsg) {
            PaymentModule.showOrderInterface();
            PaymentModule.timeout(errorMsg);
        };

        PaymentModule.check = function() {
            var orderId = getQueryString("orderId");
            AjaxUtils.post("/code.aspx?op=checkOrder", "orderId=" + orderId, function(data) {
                if (data.code == 1) {
                    window.location.href = data.data;
                } else {
                    if (data.date == null || data.date < 0) data.date = 0;
                    window.intDiff = data.date;
                    if (window.intDiff <= 0) window.intDiff = 0;
                    if (window.intDiff != 0 && data.payType != null && PaymentModule.nowPayType != data.payType) {
                        PaymentModule.switchPayType(data.payType);
                    }
                }
            });
        };



        // 初始化订单
        PaymentModule.initOrder();
    </script>
</body>
</html>
