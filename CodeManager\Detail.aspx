﻿<%@ Page Title="OCR文字识别助手-产品介绍 | AI智能文字识别软件 | 高精度表格公式识别工具 | 免费手写识别" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <meta name="description" content="OCR文字识别助手：AI智能文字识别工具、图片识别、表格提取、图片转Excel、数学公式识别、多语言翻译、批量处理。98%+识别率，支持Windows全系统，100,000+专业用户信赖之选。" />
    <meta name="keywords" content="OCR助手,免费OCR,免费文字识别,免费图片识别,免费图文识别,免费图片转文字,OCR识别,文字识别,图片识别,批量OCR,图片转文字,图片转表格,图片转公式,表格识别,文档处理,文档翻译" />

    <meta property="og:title" content="OCR助手产品介绍 | AI智能文字识别软件" />
    <meta property="og:description" content="专业AI文字识别工具，支持表格提取、公式识别、多语言翻译。98%+识别率，100,000+用户信赖。" />
    <meta property="og:type" content="website" />

    <style>
        .container-1200,.container-800,.container-850{margin:0 auto}
        .container-1200{max-width:1200px}
        .container-800{max-width:800px}
        .container-850{max-width:850px}

        .title-large,.title-small,.card-title{color:#1a1c1f;font-weight:bold;line-height:1.2}
        .title-large{font-size:2.5rem;letter-spacing:2px}
        .title-small{font-size:2rem}
        .card-title{font-size:20px}
        .title-medium{font-size:16px;color:#606879;font-weight:normal;line-height:1.3}
        .faq-question-title{font-size:1.25rem;color:#333;font-weight:600;line-height:1.3;margin:0}
        .text-muted{color:#41464f}
        .text-feature{font-size:18px;color:#41464f;line-height:1.5;font-weight:normal}
        .text-review{font-size:20px;color:#1a1c1f;line-height:1.4;margin:0;font-weight:500;text-align:center}

        .mb-0{margin-bottom:0}
        .mb-15{margin-bottom:15px}
        .mb-20{margin-bottom:20px}
        .mb-30{margin-bottom:30px}
        .mb-60{margin-bottom:60px}
        .btn-base,.btn-large{text-decoration:none;transition:all .3s;font-weight:600;text-align:center}
        .btn-base{border-radius:5px;font-size:15px}
        .btn-large{border-radius:8px;font-size:16px;min-width:180px}

        .btn-primary{background:#0056b3;color:#fff;padding:10px 20px}
        .btn-outline,.btn-outline-download{border:2px solid #1764ff;color:#1764ff;background:transparent}
        .btn-outline{padding:8px 18px}
        .btn-outline-download{padding:13px 40px}
        .btn-outline:hover,.btn-outline-download:hover{background:#1764ff;color:#fff;text-decoration:none}
        .btn-download{background:#1764ff;color:white;padding:15px 40px}

        .btn-upgrade-base{background:linear-gradient(135deg,#ff6b35 0%,#f7931e 100%);color:#fff;border:none;text-decoration:none;font-weight:600;transition:all .3s}
        .btn-upgrade,.btn-upgrade-large{box-shadow:0 2px 8px rgba(255,107,53,0.3)}
        .btn-upgrade{border-radius:5px;padding:10px 20px;font-size:15px}
        .btn-upgrade-large{border-radius:8px;padding:15px 40px;font-size:16px;box-shadow:0 3px 10px rgba(255,107,53,0.3)}
        .btn-upgrade:hover,.btn-upgrade-large:hover{background:linear-gradient(135deg,#e55a2b 0%,#e8851a 100%);color:#fff;text-decoration:none;transform:translateY(-1px)}
        .btn-upgrade:hover{box-shadow:0 4px 12px rgba(255,107,53,0.4)}
        .btn-upgrade-large:hover{box-shadow:0 5px 15px rgba(255,107,53,0.4)}

        .download-buttons{display:flex;justify-content:center;align-items:center;gap:15px;flex-wrap:wrap}
        @media (max-width:768px){.download-buttons{flex-direction:column;gap:10px}.download-buttons .button{width:100%;max-width:200px}}
        @media (max-width:480px){.download-buttons .button{min-width:100px;padding:10px 15px;font-size:14px}}

        .nav-link{color:#1a1c1f;text-decoration:none;font-weight:500;padding:10px 0;border-bottom:2px solid transparent;transition:all .3s}
        .nav-list{list-style:none;margin:0;padding:0;display:flex;justify-content:center;align-items:center;gap:40px}
        .card-content{padding:20px;background:#fff;text-align:center}
        .card-image{height:200px;display:flex;align-items:center;justify-content:center;position:relative}
        .card-img{max-width:80%;max-height:80%;object-fit:contain}
        .card-text{font-size:16px;color:#606879;line-height:1.4;margin:0;font-weight:normal}

        .bg-cover{background-size:cover;background-repeat:no-repeat}
        .bg-left-center{background-position:left center}
        .bg-right-center{background-position:right center}
        .feature-bg,.feature-bg-alt{position:relative;display:flex;align-items:center}
        .feature-bg{min-height:500px}
        .feature-bg-alt{min-height:400px}
        .feature-content-right,.feature-content-left,.feature-content-alt{position:absolute;top:0;bottom:0;width:50%;display:flex;align-items:center}
        .feature-content-right{right:0;padding:60px 80px 60px 150px}
        .feature-content-left{left:20%;padding:60px 60px 60px 80px}
        .feature-content-alt{right:0;padding:60px 80px 60px 60px}

        .user-avatar,.dynamic-user-avatar{text-align:center;cursor:pointer;transition:all .3s}
        .user-avatar img,.dynamic-user-img{width:80px;height:80px;border-radius:50%;margin-bottom:15px;border:3px solid #ddd}
        .user-avatar.active img,.dynamic-user-img.active{border-color:#00d4aa}
        .user-name{font-size:18px;font-weight:500;color:#1a1c1f;margin-bottom:5px}
        .user-role{font-size:16px;color:#606879;font-weight:normal}
        .review-item{transition:opacity .5s}
        .review-item.active{display:block;opacity:1}
        .review-item:not(.active){display:none;opacity:0}

        .link-primary{color:#0056b3;text-decoration:none;font-weight:600}
        .dynamic-faq-item{margin-bottom:20px;border:1px solid #e1e6ed;border-radius:8px;overflow:hidden}
        .dynamic-faq-header{background:#f8f9fa;padding:20px;cursor:pointer;display:flex;justify-content:space-between;align-items:center}
        .dynamic-faq-header.first{border-bottom:1px solid #e1e6ed}
        .dynamic-faq-content{padding:20px;background:#fff;border-radius:0 0 8px 8px}
        .dynamic-faq-content.show{display:block}
        .dynamic-faq-content.hide{display:none}
        .dynamic-faq-icon{font-size:18px;color:#606879;transition:transform .3s}
        .banner-container{height:280px;overflow:hidden;position:relative;width:100%;margin-top:60px}
        .banner-list{position:relative;width:100%;height:280px;margin:0;padding:0;list-style:none}
        .banner-slide{position:absolute;width:100%;height:100%;left:0;top:0;background-size:cover;background-position:center}
        .banner-dots{position:absolute;bottom:20px;left:50%;transform:translateX(-50%);z-index:10}
        .banner-dots ul{display:flex;list-style:none;margin:0;padding:0}
        .banner-dot{width:12px;height:12px;border-radius:50%;margin:0 5px;cursor:pointer;transition:all 0.3s;background:rgba(255,255,255,0.5)}
        .banner-dot.on{background:#fff}

        .nav-menu{background:#f8f9fa;padding:15px 0;border-bottom:1px solid #e1e6ed;position:relative;z-index:100;transition:all 0.3s cubic-bezier(0.4,0,0.2,1);transform:translateZ(0)}
        .nav-menu.fixed{position:fixed;top:60px;left:0;right:0;width:100%;background:rgba(248,249,250,0.98);backdrop-filter:blur(15px);-webkit-backdrop-filter:blur(15px);box-shadow:0 2px 15px rgba(0,0,0,0.08);border-bottom:1px solid rgba(225,230,237,0.8)}
        .nav-menu-placeholder{height:0;transition:height 0.3s ease}
        .nav-menu-placeholder.active{height:60px}
        @media (max-width:991px){.nav-menu.fixed{top:54px}}
        @media (max-width:768px){.nav-menu.fixed{padding:10px 0}}
        .section-padding{padding:50px 20px 0 20px}
        .section-bg-gray{background:#f8f9fa}
        .section-feature{padding:40px 0 0 0}
        .section-review,.section-faq{padding:50px 0}
        .container-padding{padding:0 20px}
        .container-feature-padding{padding:20px 20px 40px 20px}
        .cards-container{display:flex;gap:20px;justify-content:center}
        .feature-card{flex:1;max-width:350px;border-radius:10px;padding:0;overflow:hidden;position:relative;min-height:280px}
        .card-green{background:linear-gradient(135deg,#1DD1A1 0%,#10AC84 100%)}
        .card-blue{background:linear-gradient(135deg,#3742FA 0%,#2F3542 100%)}
        .card-red{background:linear-gradient(135deg,#FF6B6B 0%,#EE5A52 100%)}
        .review-quote-container{position:relative;min-height:120px}
        .review-quote{position:absolute;font-size:24px;color:#00d4aa;font-family:serif;line-height:1;z-index:1}
        .review-slider{padding:20px 80px;overflow:hidden}
        .avatars-container{gap:50px}
        .bottom-section{height:400px}
        .bottom-content{align-content:center;height:100%}
        .bottom-title{white-space:nowrap}
        .flex-button{flex:0 0 auto;min-width:120px}
    </style>
    <link rel="preconnect" href="https://lsw-fast.lenovo.com.cn" />
    <script type="application/ld+json">{"@context":"http://schema.org","@type":"SoftwareApplication","name":"OCR Assistant","description":"OCR Assistant is an efficient productivity tool that integrates text, tables, formulas, documents, and translation.","category":"Productivity","applicationCategory":"Business","image":"https://lsw-fast.lenovo.com.cn/appstore/apps/adp/logo/7273-2024-06-05074650-1717588010656.gif","screenshot":["https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/3441-2023-07-18051430-1689671670795.png","https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/4112-2023-07-18051443-1689671683656.png","https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/0653-2023-07-********-*************.png","https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/6000-2023-07-********-*************.png"],"aggregateRating":{"@type":"AggregateRating","worstRating":"1","bestRating":"5","ratingValue":"4.8","ratingCount":"<%=((System.DateTime.Now.ToUniversalTime().Ticks - 621355968000000000) / *************3).ToString() %>"},"offers":{"@type":"Offer","price":0,"priceCurrency":"USD","category":"free"},"operatingSystem": "Windows","downloadUrl":"<%=Account.Web.CommonRequest.GetDownLoadUrl(Request) %>"}</script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <%var strDownUrl = Account.Web.CommonRequest.GetDownLoadUrl(Request);  %>

    <script type="text/javascript">
        window.addEventListener('DOMContentLoaded',()=>{
            const HOST_MAP=[{old:'https://scm-file-new-**********.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/',new:'https://cdn.oldfish.cn/'},{old:'https://scm-file-new-**********.yijiupicdn.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/',new:'https://cdn.oldfish.cn/'}];
            document.querySelectorAll('img').forEach(img=>{
                img.dataset.originSrc=img.src;
                img.addEventListener('error',function handler(){
                    const targetHost=HOST_MAP.find(h=>this.src.startsWith(h.old));
                    if(targetHost&&this.src===this.dataset.originSrc){
                        this.src=this.src.replace(targetHost.old,targetHost.new);
                        this.dataset.originSrc=this.src;
                        this.removeEventListener('error',handler);
                    }
                });
            });
        });
    </script>

    <div class="camscanner_banner index_card banner-container">
        <div class="bd">
            <ul class="banner-list">
                <li data-bg="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/image/bg3.jpg" class="banner-slide active"></li>
                <li data-bg="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/image/banner1.png" class="banner-slide inactive"></li>
            </ul>
        </div>
        <div class="hd banner-dots">
            <ul>
                <li class="banner-dot on"></li>
                <li class="banner-dot"></li>
            </ul>
        </div>
    </div>

    <header style="position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); white-space: nowrap; border: 0;">
        <h1>OCR文字识别助手 - AI智能文字识别软件 - 高精度文字表格公式文档识别工具</h1>
    </header>

    <nav class="camscanner_menu nav-menu">
        <div class="container-1200 container-padding">
            <ul class="nav-list">
                <li><a class="a_anli nav-link" href="#scene-section">应用场景</a></li>
                <li><a class="a_gongneng nav-link" href="#feature-section">功能介绍</a></li>
                <li><a class="a_fangan nav-link" href="#review-section">用户评价</a></li>
                <li><a class="a_fangan nav-link" href="#faq-section">常见问题</a></li>
                <li><a href="<%=strDownUrl %>" class="btn-base btn-primary" style="background: #1764ff">免费下载</a></li>
                <li><a href="/Ocr.aspx" class="btn-base btn-outline">在线体验</a></li>
                <li><a href="/Upgrade.aspx" class="btn-upgrade-base btn-upgrade">立即升级</a></li>
            </ul>
        </div>
    </nav>

    <section id="scene-section" class="main warp a_anli_content section-padding section-bg-gray" style="padding-top: 10px;">
        <div style="max-width: 1200px; margin: 0 auto; padding: 0 20px;">
            <div class="text-center mb-60">
                <h2 class="title-large mb-20"><span class="color-blue">应用场景</span> - 电脑扫描仪，随时记录，轻松分享</h2>
                <p class="title-medium">"电脑上最好的100个软件之一"</p>
            </div>

            <div class="cards-container">
                <div class="feature-card card-green">
                    <div class="card-image">
                        <picture>
                            <source srcset="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.001.png.webp" type="image/webp">
                            <img src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.001.png" alt="轻松处理各种场景" class="card-img" loading="lazy">
                        </picture>
                    </div>
                    <div class="card-content">
                        <h3 class="card-title mb-15">智能文字识别</h3>
                        <p class="card-text">办公文档处理必备，图片文字一键转换，提升工作效率。<a href="/Ocr.aspx?type=text_recognize" class="link-primary">立即体验</a></p>
                    </div>
                </div>

                <div class="feature-card card-blue">
                    <div class="card-image">
                        <picture>
                            <source srcset="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.002.png.webp" type="image/webp">
                            <img src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.002.png" alt="自动图像识别预处理" class="card-img" loading="lazy">
                        </picture>
                    </div>
                    <div class="card-content">
                        <h3 class="card-title mb-15">表格数据提取</h3>
                        <p class="card-text">智能识别表格结构，自动提取数据内容，导出Excel格式。<a href="/Ocr.aspx?type=table" class="link-primary">立即体验</a></p>
                    </div>
                </div>

                <div class="feature-card card-red">
                    <div class="card-image">
                        <picture>
                            <source srcset="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.003.png.webp" type="image/webp">
                            <img src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.003.png" alt="打字到手酸的同学们的神器" class="card-img" loading="lazy">
                        </picture>
                    </div>
                    <div class="card-content">
                        <h3 class="card-title mb-15">批量文档处理</h3>
                        <p class="card-text">支持批量导入处理，多种格式输出，告别重复打字工作。<a href="<%=strDownUrl %>" class="link-primary">立即下载</a></p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="feature-section" class="advantage a_gongneng_content section-feature section-bg-gray">
        <div class="container-1200 text-center container-feature-padding">
            <h2 class="title-large mb-20"><span class="color-blue">功能特色</span> - AI智能引擎，精准识别，高效转换</h2>
            <p class="title-medium">三大核心技术，助力数字化办公新体验</p>
        </div>

        <div class="feature-bg bg-cover bg-left-center" data-bg="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/image/banner_a_20161021.png">
            <div class="feature-content-right">
                <div>
                    <h3 class="title-small mb-30">AI-智能OCR识别</h3>
                    <div class="text-feature">
                        <p class="mb-15">🚀 一键操作，瞬间识别图片文字内容</p>
                        <p class="mb-15">🌍 支持100+种语言，全球业务无障碍</p>
                        <p class="mb-15">☁️ 云端存储，多设备同步，随时随地使用</p>
                        <p class="mb-0">✨ 98%+识别准确率，专业品质值得信赖</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="feature-bg bg-cover bg-left-center" data-bg="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/image/banner_e.png">
            <div class="feature-content-alt">
                <div>
                    <h3 class="title-small mb-30">轻松处理各种文档</h3>
                    <div class="text-feature">
                        <p class="mb-15">📄 Office全家桶完美支持，Word/Excel/PPT轻松转换</p>
                        <p class="mb-15">📋 PDF文档智能解析，表格数据精准提取</p>
                        <p class="mb-15">🌐 全文扫描翻译，多语言文档无障碍处理</p>
                        <p class="mb-0">💾 智能解析结果，一键下载多种格式文件</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-cover bg-right-center feature-bg-alt" data-bg="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/image/banner_b_20161021.png">
            <div class="feature-content-left">
                <div>
                    <h3 class="title-small mb-30">云上OCR，轻享服务</h3>
                    <div class="text-feature">
                        <p class="mb-15">🏢 高性能云端服务器，一个账号畅享所有功能</p>
                        <p class="mb-15">💻 专为Windows系统优化，稳定流畅高效</p>
                        <p class="mb-0">🔒 数据安全可控，敏感文档本地处理更放心</p>
                    </div>
                </div>
            </div>
        </div>

    </section>

    <section id="review-section" class="advantage a_fangan_content section-review section-bg-gray">
        <div class="container-1200 text-center container-padding">
            <h2 class="title-large mb-20"><span class="color-blue">用户评价</span> - 真实反馈，口碑见证，值得信赖</h2>
            <p class="title-medium mb-60">100,000+专业用户的真实使用体验</p>

            <script type="application/ld+json">{"@context":"http://schema.org","@type":"Product","name":"OCR助手","review":[{"@type":"Review","author":{"@type":"Person","name":"John"},"reviewRating":{"@type":"Rating","ratingValue":"5","bestRating":"5"},"reviewBody":"工作原因要经常扫描东西发邮件给客户，用的机会多。OCR助手自动识别，剪裁方便，关键能有多种格式互相转换，简直逆天哦哦哦，真心推荐！！！"},{"@type":"Review","author":{"@type":"Person","name":"Abby"},"reviewRating":{"@type":"Rating","ratingValue":"5","bestRating":"5"},"reviewBody":"审计人员尤其适合使用，图片处理之后效果灰常好，很清晰，值得推荐。就查账翻凭证的时候啊，就你懂的啊，就有了OCR助手超级方便呢～"},{"@type":"Review","author":{"@type":"Person","name":"Han"},"reviewRating":{"@type":"Rating","ratingValue":"5","bestRating":"5"},"reviewBody":"门诊每天的手写病历和处方可以用OCR助手，很容易的将图片转成数字化，并存储，在没有实现数字化系统的小门诊可以很容易的实现病例和处方的讨论！有不足但是很棒的软件！"},{"@type":"Review","author":{"@type":"Person","name":"Mia"},"reviewRating":{"@type":"Rating","ratingValue":"5","bestRating":"5"},"reviewBody":"不得不说，对于学生党太好用了，书上的重点都可以OCR识别下来再排版打印，写论文到图书馆拍资料都是用的它，很喜欢。推荐给好多同学用~"},{"@type":"Review","author":{"@type":"Person","name":"Lisa"},"reviewRating":{"@type":"Rating","ratingValue":"5","bestRating":"5"},"reviewBody":"做财务工作经常需要处理各种票据和报表，OCR助手能快速识别发票、收据上的文字，大大提高了工作效率。特别是月末结账的时候，简直是救命神器！"}]}</script>

            <div class="review-quote-container">
                <div class="review-quote" style="left: 10%; top: 10px;">"</div>
                <div id="reviewSlider" class="container-850 review-slider">
                    <div id="review-content-container" class="position-relative"></div>
                </div>
                <div class="review-quote" style="right: 10%; bottom: 10px;">"</div>
            </div>
            <div style="width: 60px; height: 4px; background: #00d4aa; margin: 0 auto 60px;"></div>
            <div id="userAvatars" class="d-flex justify-content-center flex-wrap avatars-container"></div>
        </div>
    </section>

    <section id="faq-section" class="section-faq bg-white">
        <div class="container-1200 container-padding">
            <h2 class="title-large mb-20 text-center"><span class="color-blue">常见问题</span> - 快速解答，贴心服务，无忧使用</h2>
            <p class="title-medium mb-60 text-center">专业技术支持，让您的每个疑问都得到及时解答</p>
            <div id="faq-container" class="container-800"></div>
        </div>

        <script type="application/ld+json">{"@context":"http://schema.org","@type":"FAQPage","mainEntity":[{"@type":"Question","name":"OCR助手主要能做什么？","acceptedAnswer":{"@type":"Answer","text":"想象一下，再也不用对着图片一个字一个字地敲键盘了！OCR助手就是您的智能文字识别专家：\n\n🚀 核心识别功能：\n• 截图识别：工作中遇到图片文字？按个快捷键瞬间变文本\n• 图片识别：手机拍的资料、扫描的文档，拖进来就能识别\n• 文档识别：厚厚的PDF报告，一键提取全部文字内容\n• 表格识别：复杂表格秒变Excel，数据分析不再愁\n• 公式识别：数学公式直接转换，论文写作效率翻倍\n\n🌍 多语言神器：\n• 划词翻译：看到外文选中就翻译，学习工作无障碍\n• 图片翻译：国外资料图片直接翻译成中文\n• 文档翻译：整篇外文文档保持格式完美翻译\n\n支持100多种语言，让您在全球化时代游刃有余！无论您是忙碌的上班族、勤奋的学生，还是专业的研究者，这都将成为您最得力的工作伙伴。"}},{"@type":"Question","name":"软件免费吗？各版本功能有什么区别？","acceptedAnswer":{"@type":"Answer","text":"我们深知用户需求，承诺永久免费版本让您先体验！同时提供升级版本，让效率翻倍：\n\n🆓 免费版 - 轻松入门\n• 每天20次截图识别，满足基本需求\n• 零成本体验AI识别的神奇魅力\n• 适合偶尔使用的轻度用户\n\n👤 个人版 - 效率升级\n• 离线识别保护隐私，翻译功能解决语言障碍\n• 每天200次额度，2台设备随心用\n• 上班族的贴心选择，日常办公效率倍增\n\n💼 专业版 - 能力进阶\n• 表格识别让数据处理变简单，公式识别助力学术研究\n• 每天500次额度，3台设备全覆盖\n• 财务、教师、研究员的专业工具\n\n🚀 旗舰版 - 无限可能\n• 批量处理解放双手，文档识别处理海量资料\n• 每天2000次额度，5台设备企业级体验\n• 让您从重复劳动中彻底解脱，专注创造价值\n\n每个版本都物超所值，选择适合的版本，让工作生活更轻松！"}},{"@type":"Question","name":"支持离线使用吗？离线和在线识别有什么区别？","acceptedAnswer":{"@type":"Answer","text":"当然支持！个人版及以上就能享受离线识别，这是我们的独家优势：\n\n🔒 离线识别 - 隐私至上，速度飞快\n• 重要文件不用担心泄露，完全本地处理\n• 毫秒级识别速度，网络再慢也不影响工作\n• 出差路上、地铁里，随时随地高效办公\n• 公司机密文件处理的最佳选择\n\n☁️ 在线识别 - 精准强大，无所不能\n• 最新AI模型加持，识别准确率更胜一筹\n• 100多种语言支持，全球文档都能搞定\n• 复杂格式、艺术字体，统统不在话下\n\n您可以根据实际需要自由切换：处理敏感文档用离线模式保护隐私，处理复杂内容用在线模式确保精准。一个软件，两种模式，给您最大的灵活性！"}},{"@type":"Question","name":"识别准确率怎么样？","acceptedAnswer":{"@type":"Answer","text":"准确率是我们最自豪的优势！经过千万级数据训练，让您告别识别错误的烦恼：\n\n📊 准确率让您放心：\n• 中文识别：98%以上准确率，比人工录入还靠谱\n• 英文识别：99%+超高准确率，国际文档轻松搞定\n• 离线识别：95%以上准确率，隐私安全不打折\n\n✅ 什么都能识别：\n• 书籍报纸的印刷体 - 清晰完美识别\n• 复杂表格数据 - 结构完整保留\n• 手写笔记内容 - 潦草字迹也能懂\n• 古籍医方特殊文档 - 专业场景特别优化\n\n告别因为识别错误而重复修改的痛苦！我们的AI经过专业训练，就像拥有了一双永不疲倦、永不出错的眼睛，让您的工作一次到位，效率倍增！"}},{"@type":"Question","name":"识别速度快吗？操作简单吗？","acceptedAnswer":{"@type":"Answer","text":"快到让您怀疑人生！简单到闭着眼睛都会用！\n\n⚡ 速度快到飞起：\n• 离线识别：眨眼间完成，比您按Ctrl+C还快\n• 在线识别：1-2秒搞定，喝口水的功夫就好了\n• 批量处理：几十个文件一起上，解放您的双手\n\n🎯 操作简单到极致：\n• 快捷键截图：工作中看到文字，按一下就变文本\n• 拖拽识别：图片往软件里一拖，自动开始识别\n• 右键识别：文件上右键点击，识别功能随叫随到\n• 批量导入：一次选择多个文件，坐等结果就行\n\n我们的设计理念就是\"让复杂的事情变简单\"。不管您是电脑高手还是新手小白，都能在3分钟内上手，从此告别繁琐的手工录入，把时间用在更有价值的事情上！"}},{"@type":"Question","name":"表格和公式识别效果如何？","acceptedAnswer":{"@type":"Answer","text":"这是我们的拿手绝活！专业版及以上用户的超级武器：\n\n📋 表格识别 - 数据处理神器：\n• 再复杂的表格都能完美识别：合并单元格、多层表头统统搞定\n• 直接生成Excel文件，格式完美保留，拿来就能用\n• 财务报表、数据统计不再需要手工录入\n• 准确率95%+，比手工录入更可靠\n\n🧮 公式识别 - 学术研究利器：\n• 数学公式、化学方程式、物理公式一网打尽\n• 完美转换为LaTeX格式，论文写作如虎添翼\n• 复杂符号和结构都能精准识别\n• 老师、学生、研究员的最爱工具\n\n想象一下：看到一个复杂表格不用再一个个单元格录入，看到数学公式不用再手工输入复杂符号。这些专业功能让您从重复劳动中解脱，专注于分析和创造，工作效率提升10倍不是梦！"}},{"@type":"Question","name":"支持批量处理和多设备使用吗？","acceptedAnswer":{"@type":"Answer","text":"当然支持！这些贴心功能让您的使用体验更上一层楼：\n\n📦 批量处理（旗舰版专享）- 效率爆表：\n• 一次处理几十上百个文件，喝杯咖啡就全搞定\n• 统一设置输出格式，批量重命名，省心省力\n• 大量文档处理不再是噩梦，而是轻松愉快的体验\n• 让您从重复点击中彻底解放，时间就是金钱！\n\n📱 多设备授权 - 随时随地高效：\n• 个人版：家里公司两台电脑，工作生活无缝切换\n• 专业版：办公室、家里、笔记本，3台设备全覆盖\n• 旗舰版：5台设备随心用，团队协作更便捷\n\n数据云端同步，设置一次处处可用。上班路上用笔记本处理文档，到办公室无缝继续工作。真正做到\"人在哪里，高效就在哪里\"！\n\n选择合适的版本，让OCR助手成为您所有设备上的得力助手！"}},{"@type":"Question","name":"数据安全有保障吗？","acceptedAnswer":{"@type":"Answer","text":"安全是我们的生命线！我们比您更在乎您的数据安全：\n\n🔐 银行级安全防护：\n• SSL加密传输，黑客也无法窥探您的数据\n• 传输过程全程加密，安全性媲美网银系统\n\n🗑️ 用完即删，绝不留痕：\n• 云端数据24小时内自动销毁，不给任何人留机会\n• 我们承诺：绝不保存、绝不分析、绝不利用您的文档\n\n💻 离线处理，隐私无忧：\n• 个人版及以上支持完全离线识别\n• 重要文档在您的电脑上处理，数据不出门\n• 商业机密、个人隐私，100%掌控在您手中\n\n我们深知数据就是您的生命财产！选择离线模式处理敏感文档，选择在线模式享受高精度识别。无论哪种方式，我们都用最严格的标准保护您的隐私。\n\n用OCR助手，让效率提升的同时，安全感也满满！"}},{"@type":"Question","name":"遇到问题怎么办？有技术支持吗？","acceptedAnswer":{"@type":"Answer","text":"我们的服务比产品更贴心！让您用得放心，学得开心：\n\n🎧 专属客服（个人版及以上专享）：\n• 软件内一键联系客服，问题秒速响应\n• 官网在线客服随时待命，专业解答\n• 邮件支持详细回复，2小时内必回\n• 真人客服，不是机器人，沟通更有温度\n\n📚 学习资源丰富到爆：\n• 图文教程手把手教学，小白也能成专家\n• 视频指导生动直观，看一遍就会用\n• 常见问题库随时查阅，自助解决更高效\n• 功能更新第一时间通知，新功能抢先体验\n\n🏢 企业级定制服务：\n• 特殊需求？我们来定制！\n• API接口开发，无缝集成您的系统\n• 专属解决方案，让OCR完美适配您的业务\n\n我们不只是卖软件，更是您的技术伙伴！遇到问题不用慌，我们的专业团队随时为您保驾护航。选择OCR助手，选择的不仅是产品，更是一份安心的保障！\n\n软件终身免费更新，付费用户永享最新功能，让您的投资持续增值！"}}]}</script>
    </section>

    <section class="home-section section-6">
        <div class="container">
            <div class="row pt-0 justify-content-between bottom-section">
                <div class="bg-yuan"></div>
                <div class="price-header col-xxl-7 col-md-6 text-center text-md-left animated fadeInUp" data-animation="fadeInUp">
                    <div>
                        <picture>
                            <source srcset="site/image/icon/p_1.png.webp" type="image/webp">
                            <img class="section6-p-icon" src="site/image/icon/p_1.png" alt="全能生产力工具图标" aria-hidden="true" >
                        </picture>
                    </div>
                    <div class="mt-3 fn48 fnbold huoban bottom-title">
                        AI驱动的&nbsp;<span class="color-blue">生产力引擎</span>
                        <br>
                        100,000+&nbsp;专业用户的信赖之选
                    </div>
                </div>
            </div>
        </div>
    </section>
    <script>
        document.addEventListener('DOMContentLoaded',()=>{
            function initBannerSlide(){
                let current=0;
                const slides=document.querySelectorAll('.camscanner_banner .bd ul li'),dots=document.querySelectorAll('.camscanner_banner .hd ul li');
                slides.forEach(slide=>{const bgUrl=slide.getAttribute('data-bg');if(bgUrl)slide.style.backgroundImage=`url('${bgUrl}${typeof isWebP!=='undefined'&&isWebP&&bgUrl.match(/\.(jpg|png)$/)?'.webp':''}')`});
                const show=i=>{slides.forEach((slide,index)=>slide.style.display=index===i?'block':'none');dots.forEach((dot,index)=>dot.classList.toggle('on',index===i))};
                setInterval(()=>show(current=(current+1)%slides.length),5000);
                dots.forEach((dot,index)=>dot.addEventListener('click',()=>show(current=index)));
                show(0);
            }

            function initUserReviewSlide(){
                let current=0;
                const reviews=document.querySelectorAll('.review-item'),users=document.querySelectorAll('.user-avatar');
                const show=i=>{reviews.forEach((review,index)=>{const isActive=index===i;review.classList.toggle('active',isActive);review.style.cssText=isActive?'display:block;opacity:1':'display:none;opacity:0'});users.forEach((user,index)=>{user.classList.toggle('active',index===i);const img=user.querySelector('img');if(img)img.style.borderColor=index===i?'#00d4aa':'#ddd'})};
                if(reviews.length){setInterval(()=>show(current=(current+1)%reviews.length),4000);users.forEach(user=>user.addEventListener('click',()=>{const index=+user.getAttribute('data-index');if(!isNaN(index))show(current=index)}));show(0)}
            }

            const initSmoothScroll=()=>document.querySelectorAll('a[href^="#"]').forEach(link=>link.addEventListener('click',e=>{e.preventDefault();const target=document.querySelector(link.getAttribute('href'));if(target)window.scrollTo({top:target.getBoundingClientRect().top+window.pageYOffset-80,behavior:'smooth'})}));
            const initNavHighlight=()=>window.addEventListener('scroll',()=>{const scrollTop=window.pageYOffset;['#scene-section','#feature-section','#review-section','#media-section'].forEach(sectionId=>{const el=document.querySelector(sectionId);if(el){const top=el.getBoundingClientRect().top+window.pageYOffset-100;if(scrollTop>=top&&scrollTop<top+el.offsetHeight){document.querySelectorAll('.mod-item').forEach(item=>item.classList.remove('active'));const navLink=document.querySelector(`a[href="${sectionId}"]`);if(navLink)navLink.classList.add('active')}}})});

            function generateUserReviews(){
                const reviewContainer=document.getElementById('review-content-container'),avatarContainer=document.getElementById('userAvatars');
                if(!reviewContainer||!avatarContainer||reviewContainer.children.length||avatarContainer.children.length)return;
                let reviewData=null;
                document.querySelectorAll('script[type="application/ld+json"]').forEach(script=>{try{const data=JSON.parse(script.textContent);if(data['@type']==='Product'&&data.review)reviewData=data}catch(e){}});
                if(!reviewData?.review)return;
                const users={John:{image:'cam.005.png',role:'办公族'},Abby:{image:'cam.006.png',role:'审计'},Han:{image:'cam.007.png',role:'医生'},Mia:{image:'cam.008.png',role:'学生'},Lisa:{image:'cam.006.png',role:'财务'}};
                const url='<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/picture/';
                let reviewHtml='',avatarHtml='';
                reviewData.review.forEach((review,i)=>{const first=i===0;const user=users[review.author.name]||{image:'cam.005.png',role:'用户'};reviewHtml+=`<div class="review-item${first?' active':''}"><p class="text-review">${review.reviewBody}</p></div>`;avatarHtml+=`<div class="user-avatar${first?' active':''} dynamic-user-avatar" data-index="${i}"><picture><source srcset="${url}${user.image}.webp" type="image/webp"><img src="${url}${user.image}" alt="${review.author.name}" class="dynamic-user-img${first?' active':''}" loading="lazy"></picture><div class="user-name">${review.author.name}</div><div class="user-role">${user.role}</div></div>`});
                reviewContainer.innerHTML=reviewHtml;avatarContainer.innerHTML=avatarHtml;
            }

            function generateFAQ(){
                const faqContainer=document.getElementById('faq-container');
                if(!faqContainer||faqContainer.children.length)return;
                let faqData=null;
                document.querySelectorAll('script[type="application/ld+json"]').forEach(script=>{try{const data=JSON.parse(script.textContent);if(data['@type']==='FAQPage')faqData=data}catch(e){}});
                if(!faqData?.mainEntity)return;
                faqContainer.innerHTML=faqData.mainEntity.map((item,i)=>{const first=i===0;return `<div class="dynamic-faq-item"><div class="dynamic-faq-header${first?' first':''}" onclick="toggleFaq(this)"><h3 class="faq-question-title">${item.name}</h3><span class="dynamic-faq-icon">${first?'−':'+'}</span></div><div class="dynamic-faq-content ${first?'show':'hide'}"><div class="text-muted mb-0" style="line-height:1.8;white-space:pre-line">${item.acceptedAnswer.text}</div></div></div>`}).join('');
            }
            function toggleFaq(e){
                const c=e.nextElementSibling,i=e.querySelector('.dynamic-faq-icon,.faq-icon'),open=c.classList.contains('show')||c.style.display==="block";
                if(c.classList.contains('show')||c.classList.contains('hide')){c.className=c.className.replace(/show|hide/g,open?'hide':'show')}else{c.style.display=open?"none":"block"}
                i.textContent=open?"+":"−";e.style.borderBottom=open?"none":"1px solid #e1e6ed";
            }
            function initWebPBackgrounds(){document.querySelectorAll('#feature-section [data-bg]').forEach(el=>{const bgUrl=el.getAttribute('data-bg');if(bgUrl)el.style.backgroundImage=`url('${bgUrl}${typeof isWebP!=='undefined'&&isWebP&&bgUrl.match(/\.(png|jpg)$/)?'.webp':''}')`})}

            function initStickyNav(){
                const navMenu=document.querySelector('.camscanner_menu.nav-menu');
                if(!navMenu)return;
                const placeholder=document.createElement('div');
                placeholder.className='nav-menu-placeholder';
                navMenu.parentNode.insertBefore(placeholder,navMenu.nextSibling);
                const navMenuOffset=navMenu.offsetTop,navMenuHeight=navMenu.offsetHeight;
                let ticking=false;
                const handleScroll=()=>{const isFixed=(window.pageYOffset||document.documentElement.scrollTop)>=navMenuOffset;navMenu.classList.toggle('fixed',isFixed);placeholder.classList.toggle('active',isFixed);placeholder.style.height=isFixed?navMenuHeight+'px':'0px'};
                const requestTick=()=>!ticking&&(requestAnimationFrame(handleScroll),ticking=true,setTimeout(()=>ticking=false,16));
                window.addEventListener('scroll',requestTick);setTimeout(handleScroll,100);
            }
            function initModItemHover(){document.querySelectorAll('.mod-item').forEach(item=>{item.addEventListener('mouseenter',function(){const href=this.getAttribute('href');if(href&&!href.startsWith('http')){this.style.color='#007cfa';this.style.borderBottomColor='#007cfa'}});item.addEventListener('mouseleave',function(){const href=this.getAttribute('href');if(href&&!href.startsWith('http')&&!this.classList.contains('active')){this.style.color='#333';this.style.borderBottomColor='transparent'}})})}
            [initBannerSlide,generateUserReviews,initUserReviewSlide,initSmoothScroll,initNavHighlight,generateFAQ,initWebPBackgrounds,initStickyNav,initModItemHover].forEach(fn=>fn());
            window.toggleFaq=toggleFaq;
        });
    </script>
</asp:Content>
